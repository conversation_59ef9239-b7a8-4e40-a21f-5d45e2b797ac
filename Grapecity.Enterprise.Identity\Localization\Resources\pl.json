{
  // reset password
  "ResetPasswordMailSubject": "Reset Password",
  "ResetPasswordMailBody": "Hi {0},\n\nYou requested to reset the password of your \"{1}\" account, click the following link to reset it,\n\n{2}/Account/ResetPassword?Id={3}&Code={4}\n\nThanks,\n{1}",
  // user export/import
  "Id": "Id",
  "IdDesp": "The unique identifier of the user, can be empty when the provider is \"local\".",
  "Email": "Email",
  "EmailDesp": "The unique email address of the user, cannot be empty.",
  "Mobile": "Mobile",
  "MobileDesp": "The unique mobile number of the user, can be empty.",
  "Enabled": "Enabled",
  "EnabledDesp": "Indicates whether the user is enabled, can be \"true\" or \"false\", the default value is \"true\", can be empty.",
  "FalseStr": "False",
  "TrueStr": "True",
  "UserName": "UserName",
  "UserNameDesp": "The unique name of the user, cannot be empty.",
  "FirstName": "FirstName",
  "FirstNameDesp": "The first name of the user, can be empty.",
  "LastName": "LastName",
  "LastNameDesp": "The last name of the user, can be empty.",
  "FullName": "FullName",
  "FullNameDesp": "The full name of the user, can be empty.",
  "Password": "Password",
  "PasswordDesp": "The password of the user. Password cannot be empty if the provider is \"local\"",
  "Provider": "Provider",
  "ProviderDesp": "The provider of the user, the default value is \"local\", can be empty",

  "Roles": "Roles",
  "RolesDesp": "The roles of the user, can be empty. The roles can be separated by ',' and must be predefined is provided.",
  "AvailableValues": "Available Values",
  "CustomPropDesc": "Custom property.",
  "Multivalued": "Multivalued",
  "MultivaluedCustomPropDesp": "Custom property, one value per line.",
  "Sensitive": "Sensitive",
  "ValueType": "Value Type",
  "vt_Boolean": "Boolean",
  "vt_Date": "Date",
  "vt_DateTime": "DateTime",
  "vt_Float": "Float",
  "vt_Integer": "Integer",
  "vt_Text": "Text",

  "ExportFileName": "users.xlsx",
  "TemplateFileName": "users_template.xlsx",

  "UC_ImportUser_UserIdAlreadyExists": "User id already exists.",
  "UC_ImportUser_CustomPropertyNotExist": "The custom property does not exist.",
  "UC_ImportUser_EmptyExternalUserId": "The id of external user cannot be empty.",
  "UC_ImportUser_EmptyPassword": "The password cannot be empty.",
  "UC_ImportUser_InvalidPassword": "Invalid password format. Password must contain at least 1 lowercase letter, 1 uppercase letter and 1 number, and the length must be between 8-150.",
  "UC_ImportUser_EmptyUserEmail": "User email cannot be empty.",
  "UC_ImportUser_UserEmailAlreadyExists": "User email already exists.",
  "UC_ImportUser_EmptyUserName": "User name cannot be empty.",
  "UC_ImportUser_InvalidCustomPropertyValue": "Invalid custom property value detected.",
  "UC_ImportUser_InvalidEmailAddress": "Invalid email address.",
  "UC_ImportUser_InvalidEnabledValue": "Invalid value for field 'enabled', it must be 'true' or 'false'.",
  "UC_ImportUser_RoleNotExist": "Role \"{0}\" does not exist.",
  "UC_ImportUser_UserMobileAlreadyExists": "User mobile already exists.",
  "UC_ImportUser_UserNameAlreadyExists": "User name already exists.",
  "UC_InvalidUserTemplateFormat": "Invalid user template format.",
  "UC_LanguageNotSupport": "Not supported language.",
  "UC_NoUserDetected": "No user detected.",
  "UC_NoVisibleSpreadSheet": "No visible spreadsheet detected."
}